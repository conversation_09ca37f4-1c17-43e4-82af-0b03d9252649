{"concurrency": {"maxConcurrent": 100, "maxWorkers": 20, "workerConcurrency": 10, "batchSize": 20}, "network": {"loginTimeout": 8000, "maxSockets": 200, "maxFreeSockets": 50, "keepAliveTimeout": 60000}, "performance": {"retryAttempts": 1, "retryDelay": 500, "batchDelay": 50}, "presets": {"conservative": {"maxConcurrent": 20, "maxWorkers": 4, "workerConcurrency": 5, "loginTimeout": 15000}, "aggressive": {"maxConcurrent": 200, "maxWorkers": 30, "workerConcurrency": 15, "loginTimeout": 5000}, "extreme": {"maxConcurrent": 500, "maxWorkers": 50, "workerConcurrency": 20, "loginTimeout": 3000}}}