# Acronis Authentication Validator (Multi-threaded & Optimized)

A high-performance tool for batch validation of Acronis account credentials. This tool uses multi-threading and network optimization to quickly validate email:password pairs against the Acronis authentication system, sorting results into good and bad credential files.

## Features

- 🚀 **Multi-threaded processing** using worker threads for maximum performance
- ⚡ **Network optimization** with connection pooling and keep-alive
- 🔄 **Concurrent validation** using all CPU cores efficiently
- ✅ **Fast login-only validation** - no unnecessary verification steps
- ✅ **Handles multiple account scenarios** (409 errors)
- ✅ **Comprehensive error handling** and detailed logging
- ✅ **Results sorted** into good/bad credential files
- ✅ **Performance metrics** showing validation rate and timing
- ✅ **Configurable settings** for optimal performance

## Installation

The validator uses the same dependencies as the main search tool:

```bash
npm install
```

## Usage

### Basic Usage

```bash
node acronis-validator.js <credentials-file>
```

### Example

```bash
node acronis-validator.js sample-credentials.txt
```

## Input File Format

Create a text file with one email:password pair per line, separated by a colon:

```
<EMAIL>:password123
<EMAIL>:mypassword456
<EMAIL>:securepass789
```

- Lines starting with `#` are treated as comments and ignored
- Empty lines are ignored
- Email and password will be trimmed of whitespace

## Output Files

The validator creates two output files:

### good.txt
Contains successfully authenticated accounts in the same `email:password` format:
```
<EMAIL>:password123
<EMAIL>:securepass789
```

### bad.txt
Contains failed authentication accounts in the same `email:password` format:
```
<EMAIL>:mypassword456
```

## Authentication Flow

The validator performs a simple and fast login check:

1. **Initial Login**: Attempts login with email/password
2. **Multiple Accounts**: Handles 409 errors by testing each sub-account
3. **Success Criteria**: Login response status 200 = valid credentials

## Error Handling

The validator handles common Acronis authentication issues:

- **409 Multiple Accounts**: Automatically detects and validates each sub-account
- **Terms Acceptance**: Attempts to accept missing terms automatically
- **Network Timeouts**: Configurable timeout settings
- **Rate Limiting**: Built-in delays between batches

## Configuration

You can modify the configuration at the top of `acronis-validator.js`:

```javascript
const config = {
    loginTimeout: 60000,      // Login timeout in milliseconds
    retryAttempts: 1,         // Number of retry attempts per account
    retryDelay: 2000,         // Delay between retries in milliseconds
    maxConcurrent: 10         // Maximum concurrent validations
};
```

## Logging

The validator provides detailed logging:

- `[INFO]`: General information and progress
- `[SUCCESS]`: Successful authentication
- `[WARNING]`: Non-fatal issues (e.g., terms acceptance failures)
- `[ERROR]`: Authentication failures

Example log output:
```
[2024-01-15T10:30:00.000Z] [INFO] [<EMAIL>] Starting login check
[2024-01-15T10:30:01.000Z] [SUCCESS] [<EMAIL>] Login successful
```

## Performance

- **Batch Processing**: Processes accounts in configurable batches (default: 10)
- **Concurrent Validation**: Multiple accounts validated simultaneously
- **Rate Limiting**: Automatic delays to prevent API rate limiting
- **Memory Efficient**: Processes large credential files without memory issues

## Troubleshooting

### Common Issues

1. **Network Errors**: Check internet connection and firewall settings
2. **Rate Limiting**: Reduce `maxConcurrent` setting if getting rate limited
3. **Invalid Credentials**: Ensure credentials file format is correct
4. **Timeout Errors**: Increase `loginTimeout` for slow connections

### Debug Mode

For detailed debugging, you can modify the logging level in the code or add additional console.log statements.

## Security Notes

- Credential files contain sensitive information - handle securely
- Output files contain valid credentials - protect appropriately
- Consider using environment variables for sensitive configurations
- The tool makes actual login attempts - use responsibly

## Integration

The validator can be integrated into larger workflows:

```javascript
const { validateSingleAccount, loadCredentials } = require('./acronis-validator.js');

// Validate a single account
const result = await validateSingleAccount('<EMAIL>', 'password123');

// Load and process credentials
const credentials = await loadCredentials('my-credentials.txt');
```

## Support

This tool is based on the existing Acronis search infrastructure and follows the same authentication patterns. For issues or improvements, refer to the main project documentation.
