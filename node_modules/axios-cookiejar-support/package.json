{"name": "axios-cookiejar-support", "version": "5.0.5", "description": "Add tough-cookie support to axios.", "keywords": ["axios", "cookie", "cookiejar", "cookies", "tough-cookie"], "homepage": "https://github.com/3846masa/axios-cookiejar-support#readme", "bugs": {"url": "https://github.com/3846masa/axios-cookiejar-support/issues"}, "repository": {"type": "git", "url": "git+https://github.com/3846masa/axios-cookiejar-support.git"}, "funding": "https://github.com/sponsors/3846masa", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "main": "dist/index.js", "browser": "noop.js", "types": "dist/index.d.ts", "files": ["dist", "noop.js", "!**/__tests__"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc --project tsconfig.build.json", "format": "pnpm run --sequential \"/^format:.*/\"", "format:eslint": "eslint --fix .", "format:prettier": "prettier --write .", "prelint": "pnpm run build", "lint": "pnpm run \"/^lint:.*/\"", "lint:eslint": "eslint .", "lint:prettier": "prettier --check .", "lint:tsc": "tsc --noEmit", "semantic-release": "semantic-release", "test": "NODE_OPTIONS=\"--experimental-vm-modules\" jest"}, "dependencies": {"http-cookie-agent": "^6.0.8"}, "devDependencies": {"@3846masa/configs": "github:3846masa/configs#cc0d6cce430487cc6e972676837632a18a2ac4b8", "@babel/plugin-proposal-explicit-resource-management": "7.25.9", "@babel/preset-env": "7.26.0", "@babel/preset-typescript": "7.26.0", "@jest/globals": "29.7.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "6.0.3", "@semantic-release/git": "10.0.1", "@types/eslint": "9.6.1", "@types/node": "18.19.68", "axios": "1.7.9", "babel-jest": "29.7.0", "disposablestack": "1.1.7", "jest": "29.7.0", "rimraf": "5.0.10", "semantic-release": "19.0.5", "tough-cookie": "5.0.0", "typescript": "5.7.2"}, "peerDependencies": {"axios": ">=0.20.0", "tough-cookie": ">=4.0.0"}, "packageManager": "pnpm@9.15.0", "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public"}, "pnpm": {"patchedDependencies": {"@semantic-release/git@10.0.1": "patches/@<EMAIL>"}}}