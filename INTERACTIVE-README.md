# 🔐 Acronis Authentication Validator - Interactive Mode

A high-performance, interactive command-line tool for batch validation of Acronis credentials with configurable concurrency settings.

## ✨ Features

### 🎮 **Interactive Configuration**
- **Welcome Screen** with system information and current settings
- **Performance Recommendations** based on your system capabilities
- **Preset Configurations** (Conservative/Aggressive/Extreme/Custom)
- **Input Validation** with helpful error messages and range checking
- **Configuration Summary** with visual formatting
- **Preference Saving** for future use

### 🚀 **High-Concurrency Performance**
- **50-500+ parallel executions** simultaneously
- **Multi-threaded processing** using worker threads
- **Network optimization** with connection pooling and keep-alive
- **Configurable timeouts** and delays for optimal performance
- **Real-time metrics** showing processing rate and concurrency

### 🛡️ **Robust Validation**
- **Simple login check** without unnecessary verification steps
- **Multiple account handling** (409 error scenarios)
- **Comprehensive error reporting** with HTTP status codes
- **Results sorting** into good/bad credential files

## 🚀 Quick Start

### Interactive Mode (Recommended)
```bash
node acronis-validator-interactive.js
```

### Direct Mode (Using Saved Preferences)
```bash
node acronis-validator-interactive.js credentials.txt
```

### Help
```bash
node acronis-validator-interactive.js --help
```

## 📋 Interactive Flow

### 1. Welcome Screen
```
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🔐 ACRONIS AUTHENTICATION VALIDATOR v2.0             ║
║              High-Concurrency Interactive Mode              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

💻 System Information:
   CPU Cores: 8
   Platform: darwin
   Memory: 16 GB
```

### 2. Performance Recommendations
Based on your system, the tool provides tailored recommendations:
- **4 cores or less**: Conservative settings (20-50 concurrent)
- **5-8 cores**: Balanced settings (50-100 concurrent)
- **8+ cores**: Aggressive settings (100-200+ concurrent)

### 3. Preset Selection
Choose from optimized presets or configure custom settings:

| Preset | Concurrent | Workers | Per-Worker | Timeout | Best For |
|--------|------------|---------|------------|---------|----------|
| **Conservative** | 20 | 4 | 5 | 15s | Slower networks, limited resources |
| **Aggressive** | 200 | 30 | 15 | 5s | High-performance systems |
| **Extreme** | 500 | 50 | 20 | 3s | Maximum throughput |
| **Custom** | User-defined | User-defined | User-defined | User-defined | Fine-tuned control |

### 4. Custom Configuration
If you choose custom, configure each setting individually:
- **Login Timeout**: 1-60 seconds (how long to wait for login response)
- **Max Concurrent**: 10-500 (total parallel connections)
- **Worker Threads**: 1-50 (number of worker processes)
- **Per-Worker Concurrency**: 1-20 (parallel requests per worker)
- **Batch Delay**: 0-5000ms (delay between batches)

### 5. Configuration Summary
```
📋 Configuration Summary:
╭─────────────────────────────────────────────────────────╮
│                    VALIDATION SETTINGS                 │
├─────────────────────────────────────────────────────────┤
│ Login Timeout:           8s                            │
│ Max Concurrent:          100                           │
│ Worker Threads:          20                            │
│ Per-Worker Concurrency:  10                           │
│ Batch Delay:             50ms                         │
│ Max Sockets:             200                           │
╰─────────────────────────────────────────────────────────╯

🔥 Performance Metrics:
   Peak Parallel Requests: 100
   Total Theoretical Max:  200
   Network Optimization:   Enabled
```

## 📁 File Format

Credentials file should contain one email:password pair per line:
```
<EMAIL>:password123
<EMAIL>:mypassword
<EMAIL>:secretpass
```

## 📊 Output Files

- **`good.txt`** - Successfully authenticated accounts
- **`bad.txt`** - Failed authentication accounts with error details
- **`validator-preferences.json`** - Saved configuration for future use

## ⚙️ Configuration Options

### Timeout Settings
- **Short (3-5s)**: Fast failures, higher throughput, may miss slow responses
- **Medium (8-10s)**: Balanced approach, good for most scenarios
- **Long (15s+)**: Catches slow responses, lower throughput

### Concurrency Levels
- **Low (10-50)**: Safe for limited bandwidth or older systems
- **Medium (50-100)**: Good balance for most modern systems
- **High (100-200)**: High-performance systems with good network
- **Extreme (200-500)**: Maximum throughput, requires powerful system

### Worker Configuration
- **Workers**: More workers = better CPU utilization
- **Per-Worker Concurrency**: Higher values = more network load per worker
- **Total Concurrency**: Workers × Per-Worker Concurrency

## 🔧 Performance Tuning

### For Maximum Speed
```
Preset: Extreme
Timeout: 3s
Concurrent: 500
Workers: 50
Per-Worker: 20
```

### For Stability
```
Preset: Conservative  
Timeout: 15s
Concurrent: 20
Workers: 4
Per-Worker: 5
```

### For Balance
```
Preset: Aggressive
Timeout: 5s
Concurrent: 200
Workers: 30
Per-Worker: 15
```

## 📈 Performance Metrics

The tool provides real-time performance information:
- **Processing Rate**: Credentials validated per second
- **Peak Concurrency**: Maximum parallel requests achieved
- **Success Rate**: Percentage of successful authentications
- **Total Time**: Complete processing duration

## 🛠️ System Requirements

- **Node.js** 14+ with worker_threads support
- **Memory**: 2GB+ recommended for high concurrency
- **Network**: Stable internet connection
- **CPU**: Multi-core recommended for worker threads

## 🔍 Troubleshooting

### Common Issues
1. **Low performance**: Try reducing concurrency or increasing timeout
2. **Network errors**: Reduce concurrent connections or add delays
3. **Memory issues**: Reduce worker count or batch sizes
4. **Timeout errors**: Increase login timeout value

### Error Messages
- **"Timeout must be between 1 and 60 seconds"**: Enter valid timeout range
- **"File not found"**: Check credentials file path
- **"No valid credentials found"**: Verify file format (email:password per line)

## 📝 Examples

### Quick Validation (Interactive)
```bash
node acronis-validator-interactive.js
# Follow prompts, select "aggressive" preset
# Enter your credentials file path
```

### Batch Processing (Direct)
```bash
node acronis-validator-interactive.js large-credential-list.txt
# Uses saved preferences or defaults
```

### Custom High-Concurrency
```bash
node acronis-validator-interactive.js
# Select "custom"
# Set concurrent: 300, workers: 40, per-worker: 15
# Timeout: 5s, delay: 25ms
```

## 🎯 Best Practices

1. **Start Conservative**: Begin with lower settings and increase gradually
2. **Monitor Performance**: Watch for network errors or timeouts
3. **Save Preferences**: Use interactive mode to save optimal settings
4. **Test Small Batches**: Validate configuration with small credential sets first
5. **Network Consideration**: Adjust settings based on your internet speed

---

**Happy Validating!** 🚀
